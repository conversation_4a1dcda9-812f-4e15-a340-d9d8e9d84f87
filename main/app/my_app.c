/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.c
 * @description: 
 * @author: 
 * @date: 2025-07-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "sk_common.h"
#include "sk_log.h"
#include "sk_websocket.h"
#include "sk_opus_dec.h"
#include "sk_os.h"
#include "sk_sm.h"
#include "my_app.h"

static const char *TAG = "MyApp";

// WebSocket连接状态
static bool ws_connected = false;

// 统计信息
static uint32_t success_count = 0;
static uint32_t error_count = 0;

// 状态机句柄
static void *g_smHandler = NULL;

void MyAppStartWebSocketConnection(void) {
    if (!ws_connected) {
        SkWsStartConnect();
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
        SkOpusDecUnmuteRemote();
    }
}

void MyAppOnWsAudioData(void *arg, void *data, uint16_t len) {
    WsAudioPacket *pkt;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    const uint16_t headerSize = 8;  // 固定包头大小：1+1+2+2+2 = 8字节

    // 检查数据指针和最小长度
    if (data == NULL || len < headerSize) {
        SK_LOGE(TAG, "Invalid data: data=%p, len=%d, min_len=%d", data, len, headerSize);
        error_count++;
        return;
    }

    pkt = (WsAudioPacket *)data;

    // 验证数据格式
    if (pkt->version != 1 || pkt->type != 1) {
        SK_LOGE(TAG, "Invalid packet format: ver=%d, type=%d", pkt->version, pkt->type);
        error_count++;
        return;
    }

    // 验证负载长度（实际数据长度 = 总长度 - 包头长度）
    uint16_t actualPayloadLen = len - headerSize;
    if (pkt->payloadLen != actualPayloadLen) {
        SK_LOGE(TAG, "Payload length mismatch: expected=%d, actual=%d",
                pkt->payloadLen, actualPayloadLen);
        error_count++;
        return;
    }

    // 检查负载长度是否合理（Opus帧通常在20-200字节之间）
    if (pkt->payloadLen == 0 || pkt->payloadLen > 1024) {
        SK_LOGE(TAG, "Invalid payload length: %d", pkt->payloadLen);
        error_count++;
        return;
    }

    // 构造时间戳记录
    timeRecord.decRxTick = SkOsGetTickCnt();

    // 检测数据类型并相应处理
    if (pkt->payloadLen >= 2 && pkt->data[0] == 0xFC && pkt->data[1] == 0x00) {
        // 处理伪Opus数据（向后兼容）
        SK_LOGW(TAG, "Received pseudo-Opus data (deprecated): seq=%d, len=%d",
                pkt->seqNum, pkt->payloadLen);
        error_count++;
        return; // 不再支持伪Opus数据
    } else {
        
        // 使用栈缓冲区（Opus帧通常不超过200字节，加上4字节头部）
        uint16_t total_frame_size = pkt->payloadLen + 4;

        if (total_frame_size > 1028) { // 1024 + 4字节的安全检查
            SK_LOGE(TAG, "Opus frame too large: %d bytes", total_frame_size);
            error_count++;
            return;
        }

        uint8_t opus_frame[1028]; // 栈缓冲区，避免malloc/free开销

        // 安全地构建完整的Opus帧
        // 前4字节：seqNum(2字节) + payloadLen(2字节)
        opus_frame[0] = pkt->seqNum & 0xFF;           // seqNum低字节
        opus_frame[1] = (pkt->seqNum >> 8) & 0xFF;    // seqNum高字节
        opus_frame[2] = pkt->payloadLen & 0xFF;       // payloadLen低字节
        opus_frame[3] = (pkt->payloadLen >> 8) & 0xFF; // payloadLen高字节

        // 复制Opus数据
        memcpy(opus_frame + 4, pkt->data, pkt->payloadLen);

        // 调用解码器
        int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0xFFFF,
                                         opus_frame, total_frame_size, &timeRecord);

        if (ret == SK_RET_SUCCESS) {
            success_count++;
            // 每100帧打印一次统计信息
            if (success_count % 100 == 0) {
                SK_LOGI(TAG, "Opus decode stats: success=%u, error=%u, seq=%d, len=%d",
                        success_count, error_count, pkt->seqNum, pkt->payloadLen);

                // 显示帧头信息（调试用）
                SK_LOGD(TAG, "Frame header: [%02X %02X %02X %02X] + %d bytes opus data",
                        opus_frame[0], opus_frame[1], opus_frame[2], opus_frame[3], pkt->payloadLen);
            }
        } else {
            error_count++;
            SK_LOGE(TAG, "Opus decode failed: seq=%d, len=%d, ret=%d",
                    pkt->seqNum, pkt->payloadLen, ret);
            SK_LOGE(TAG, "Frame header: [%02X %02X %02X %02X], opus_data: [%02X %02X %02X %02X]",
                    opus_frame[0], opus_frame[1], opus_frame[2], opus_frame[3],
                    opus_frame[4], opus_frame[5], opus_frame[6], opus_frame[7]);
        }
    }
}

void MyAppGetWebSocketStats(uint32_t *success, uint32_t *error) {
    if (success) *success = success_count;
    if (error) *error = error_count;
}

void MyAppResetWebSocketStats(void) {
    success_count = 0;
    error_count = 0;
    SK_LOGI(TAG, "WebSocket statistics reset");
}

bool MyAppIsWebSocketConnected(void) {
    return ws_connected;
}

void MyAppSetWebSocketConnected(bool connected) {
    ws_connected = connected;
    SK_LOGI(TAG, "WebSocket connection status: %s", connected ? "Connected" : "Disconnected");
}

void MyAppOnWsEvent(void *arg, void *data, uint16_t len) {
    MyAppOnWsAudioData(arg, data, len);
}

void MyAppMainCmdProc(int32_t cmd, void *smHandler) {
    if (smHandler == NULL) {
        smHandler = g_smHandler;  // 使用保存的句柄
    }

    if (smHandler == NULL) {
        SK_LOGE(TAG, "State machine handler is NULL");
        return;
    }

    // 处理特殊命令（可选：保留手动重连功能）
    switch (cmd) {
        case SPEECH_CMD_EVENT_CONFIG:  // "配置" 或 "设置" 命令可重新连接WebSocket
            MyAppStartWebSocketConnection();
            SK_LOGI(TAG, "WebSocket connection triggered by voice command (manual reconnect)");
            break;
        default:
            // 调用状态机处理其他命令
            SkSmSendEvent(smHandler, SM_EVENT_CMD, cmd, 0, 0);
            break;
    }
    return;
}

void MyAppInit(void *smHandler) {
    g_smHandler = smHandler;
    success_count = 0;
    error_count = 0;
    ws_connected = false;
    SK_LOGI(TAG, "MyApp module initialized");
}
