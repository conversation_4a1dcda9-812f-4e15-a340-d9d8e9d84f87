# WebSocket接收数据流到终端播放全面解析

## 🎯 **系统架构概览**

sk-terminal的WebSocket音频接收系统实现了从服务器接收Opus编码音频数据，经过解码处理后在终端播放的完整流程。

### **核心模块组成**
```
[WebSocket服务器] → [WebSocket客户端] → [音频数据解析] → [Opus解码] → [音频播放] → [扬声器硬件]
       ↓                ↓                ↓              ↓           ↓            ↓
  Python服务器      SkWebsocket      MyAppOnWsAudioData  SkOpusDec   SkPlayer   ES8311 DAC
```

## 📊 **完整数据流程图**

```mermaid
graph TD
    A[WebSocket服务器] --> B[TCP Socket接收]
    B --> C[SkWsProcData协议解析]
    C --> D[SkWsProcFrame帧处理]
    D --> E[MyAppOnWsEvent回调]
    E --> F[MyAppOnWsAudioData数据处理]
    F --> G[WsAudioPacket解析]
    G --> H[Opus帧重组]
    H --> I[SkOpusDecPlayRemote解码]
    I --> J[SkOpusDecFeedPlayAudio播放回调]
    J --> K[SkPlayerTask播放任务]
    K --> L[SkBspPlayAudio硬件输出]
    L --> M[AudioDevWrite DAC输出]
    M --> N[ES8311扬声器]
```

## 🔧 **详细流程分析**

### **阶段1: WebSocket连接和初始化**

#### **1.1 WebSocket模块初始化**
```c
// main/app/main.c:144-147
SkWsInit();                                    // WebSocket模块初始化
SkWsStart();                                   // 启动WebSocket服务
MyAppInit(g_smHandler);                        // 初始化my_app模块
SkWsRegOnBinDataCallback(MyAppOnWsEvent, NULL); // 注册二进制数据回调
```

#### **1.2 WebSocket连接建立**
```c
// main/app/my_app.c:34
void MyAppStartWebSocketConnection(void) {
    if (!ws_connected) {
        SkWsStartConnect();                    // 启动WebSocket连接
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
        SkOpusDecUnmuteRemote();               // 启用远程音频解码
    }
}
```

#### **1.3 回调函数注册**
```c
// main/protocol/sk_websocket.c:99
void SkWsRegOnBinDataCallback(SkWsDataCallback_t callback, void *arg) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    ctrl->onBinaryFrameCb = callback;          // 设置二进制帧回调
    ctrl->onBinaryFrameArg = arg;
}
```

### **阶段2: WebSocket数据接收**

#### **2.1 Socket数据接收循环**
```c
// main/protocol/sk_websocket.c:325-332
while (ctrl->connected == true) {
    // 等待连接事件
    event = xEventGroupWaitBits(ctrl->eventGroup, flag, pdFALSE, pdFALSE, portMAX_DELAY);
    
    // 从Socket读取数据到缓冲区
    freeSize = SK_WS_BUFF_SIZE - lastBytes;
    bytes = recv(ctrl->sock, &buffer[lastBytes], freeSize, 0);
    if (bytes <= 0) continue;
    
    lastBytes += bytes;
    // 处理接收到的数据
    procLen = SkWsProcData(ctrl, buffer, lastBytes);
}
```

#### **2.2 WebSocket协议解析**
```c
// main/protocol/sk_websocket.c:627
int32_t SkWsProcData(SkWebsocket_t *ctrl, uint8_t *buffer, int dataLen) {
    uint8_t opcode;
    uint32_t payloadLen;
    uint8_t *payload = NULL;
    
    while (lastLen >= SK_WS_FRAME_HEADER_LEN) {
        // 解析WebSocket帧头，获取载荷数据
        ret = SkWsGetPayload(&buffer[procLen], lastLen, &opcode, &payload, &payloadLen);
        if (ret < 0) break;
        
        // 处理帧数据
        SkWsProcFrame(ctrl, opcode, payload, payloadLen);
        lastLen -= ret;
        procLen += ret;
    }
    return procLen;
}
```

#### **2.3 帧数据处理**
```c
// main/protocol/sk_websocket.c:596
int32_t SkWsProcFrame(SkWebsocket_t *ctrl, uint8_t opcode, uint8_t *data, int dataLen) {
    switch (opcode) {
        case SK_WS_FRAME_TYPE_BINARY:
            if (ctrl->onBinaryFrameCb) {
                // 调用注册的二进制数据回调函数
                ctrl->onBinaryFrameCb(ctrl->onBinaryFrameArg, data, dataLen);
            }
            return SK_RET_SUCCESS;
        // ... 其他帧类型处理
    }
}
```

### **阶段3: 音频数据解析**

#### **3.1 WebSocket事件回调**
```c
// main/app/my_app.c:159
void MyAppOnWsEvent(void *arg, void *data, uint16_t len) {
    MyAppOnWsAudioData(arg, data, len);        // 委托给音频数据处理函数
}
```

#### **3.2 音频数据包解析**
```c
// main/app/my_app.c:43
void MyAppOnWsAudioData(void *arg, void *data, uint16_t len) {
    WsAudioPacket *pkt;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    const uint16_t headerSize = 8;  // 固定包头大小：1+1+2+2+2 = 8字节
    
    // 数据有效性检查
    if (data == NULL || len < headerSize) {
        SK_LOGE(TAG, "Invalid data: data=%p, len=%d", data, len);
        error_count++;
        return;
    }
    
    pkt = (WsAudioPacket *)data;
    
    // 验证数据格式
    if (pkt->version != 1 || pkt->type != 1) {
        SK_LOGE(TAG, "Invalid packet format: ver=%d, type=%d", pkt->version, pkt->type);
        error_count++;
        return;
    }
    
    // 验证负载长度
    uint16_t actualPayloadLen = len - headerSize;
    if (pkt->payloadLen != actualPayloadLen) {
        SK_LOGE(TAG, "Payload length mismatch: expected=%d, actual=%d",
                pkt->payloadLen, actualPayloadLen);
        error_count++;
        return;
    }
}
```

#### **3.3 Opus帧重组**
```c
// main/app/my_app.c:100-115
// 使用栈缓冲区（Opus帧通常不超过200字节，加上4字节头部）
uint16_t total_frame_size = pkt->payloadLen + 4;
uint8_t opus_frame[1028]; // 栈缓冲区，避免malloc/free开销

// 安全地构建完整的Opus帧
// 前4字节：seqNum(2字节) + payloadLen(2字节)
opus_frame[0] = pkt->seqNum & 0xFF;           // seqNum低字节
opus_frame[1] = (pkt->seqNum >> 8) & 0xFF;    // seqNum高字节
opus_frame[2] = pkt->payloadLen & 0xFF;       // payloadLen低字节
opus_frame[3] = (pkt->payloadLen >> 8) & 0xFF; // payloadLen高字节

// 复制Opus数据
memcpy(opus_frame + 4, pkt->data, pkt->payloadLen);

// 构造时间戳记录
timeRecord.decRxTick = SkOsGetTickCnt();

// 调用解码器
int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0xFFFF,
                                 opus_frame, total_frame_size, &timeRecord);
```

### **阶段4: Opus解码处理**

#### **4.1 远程音频解码入口**
```c
// main/opus_coder/sk_opus_dec.c:516
int32_t SkOpusDecPlayRemote(SkOpusDecHandler handler, uint16_t sessionId, 
    uint8_t *data, int32_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    SkAudioBuf *audioBuf = NULL;
    SkOpusMsg msg;
    
    // 检查解码器状态
    if (!ctrl->decRemoteEnable || ctrl->localEndFlag == 0) {
        return SK_RET_SUCCESS;
    }
    
    // 参数验证
    if (data == NULL || len <= 0 || len > AUDIO_SHORT_BUF_SIZE) {
        return SK_RET_INVALID_PARAM;
    }
    
    // 获取音频缓冲区
    audioBuf = SkAudioBufferGetFree(ctrl->inQueue, 0);
    if (audioBuf == NULL) {
        return SK_RET_NO_MEMORY;
    }
    
    // 复制数据和时间记录
    memcpy(audioBuf->data, data, len);
    if (timeRecord != NULL) {
        memcpy(&audioBuf->timeRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    }
    audioBuf->length = len;
    audioBuf->sessionId = sessionId;
    
    // 发送到解码器消息队列
    msg.event = SK_OPUS_EVENT_PLAY_REMOTE;
    msg.arg = audioBuf;
    xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY);
    
    return SK_RET_SUCCESS;
}
```

#### **4.2 Opus解码处理**
```c
// main/opus_coder/sk_opus_dec.c:287
static void SkOpusDecRemoteData(SkOpusDecHandler handler, SkAudioBuf *inBuf, SkAudioBuf *outBuf) {
    uint16_t sampleCnt = 0;
    SkOpusDecCtrl *ctrl = (SkOpusDecCtrl *)handler;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)outBuf->timeRecord;
    
    if (inBuf->length == 0) {
        outBuf->length = 0;
        return;
    }
    
    ctrl->audioMsgCnt++;
    // 调用Opus解码器，跳过前4字节头部
    sampleCnt = SkOpusDecDecode((SkOpusDecHandler)ctrl, inBuf->data + 4, inBuf->length - 4,
        (int16_t *)outBuf->data, ctrl->frameSize);
    
    outBuf->length = sampleCnt * sizeof(int16_t);
    memcpy(&outBuf->timeRecord, &inBuf->timeRecord, sizeof(inBuf->timeRecord));
    timeRecord->decDoneTick = SkOsGetTickCnt();  // 记录解码完成时间
}
```

### **阶段5: 音频播放**

#### **5.1 播放数据回调**
```c
// main/opus_coder/sk_opus_dec.c:463
size_t SkOpusDecFeedPlayAudio(uint16_t *buff, size_t len, SkAudioDownlinkTimeRecord *timeRecord) {
    size_t outLen = len;
    SkOpusDecCtrl *ctrl = &g_opusDecCtrl;
    SkAudioBuf *audioBuf = NULL;
    
    // 从输出队列获取解码后的音频数据
    audioBuf = SkAudioBufferGetData(ctrl->outQueue, 100);
    if (audioBuf == NULL) {
        return 0;  // 没有数据可播放
    }
    
    // 复制音频数据到播放缓冲区
    if (audioBuf->length > 0) {
        size_t copyLen = (audioBuf->length < len) ? audioBuf->length : len;
        memcpy(buff, audioBuf->data, copyLen);
        outLen = copyLen;
        
        // 复制时间记录
        if (timeRecord != NULL) {
            memcpy(timeRecord, &audioBuf->timeRecord, sizeof(SkAudioDownlinkTimeRecord));
            timeRecord->playTick = SkOsGetTickCnt();  // 记录播放时间
        }
    }
    
    // 释放音频缓冲区
    SkAudioBufferPutFree(ctrl->outQueue, audioBuf);
    return outLen;
}
```

#### **5.2 播放任务主循环**
```c
// main/audio/sk_player.c:131
void SkPlayerTask(void *args) {
    SkPlayerCtrl *ctrl = &g_playerCtrl;
    size_t w_bytes, rcv_bytes;
    
    // 预加载数据
    while (w_bytes == rcv_bytes && rcv_bytes != 0) {
        rcv_bytes = PlayerGetData(ctrl, (uint16_t *)ctrl->buffer, ctrl->bufferSize);
        SkBspPlayPreload((int16_t *)ctrl->buffer, rcv_bytes, &w_bytes);
    }
    
    while (ctrl->taskFlag) {
        if (ctrl->pmMode) {
            vTaskDelay(100 / portTICK_PERIOD_MS);
            continue;
        }
        
        // 启动扬声器
        SkBspStartSpk();
        
        while (!ctrl->pmMode) {
            // 获取播放数据
            rcv_bytes = PlayerGetData(ctrl, (uint16_t *)ctrl->buffer, ctrl->bufferSize);
            if (rcv_bytes == 0) continue;
            
            // 写入I2S播放
            if (SkBspPlayAudio((int16_t *)ctrl->buffer, rcv_bytes, portMAX_DELAY) == ESP_OK) {
                ESP_LOGD(TAG, "Write Task: i2s write %d bytes", rcv_bytes);
            }
        }
        
        SkBspStopSpk();  // 停止扬声器
    }
}
```

#### **5.3 获取播放数据**
```c
// main/audio/sk_player.c:54
size_t PlayerGetData(SkPlayerCtrl *ctrl, uint16_t *buff, size_t len) {
    size_t rcv_bytes = len;
    SkAudioDownlinkTimeRecord timeRecord;
    
    if (ctrl->funcGetPlayData != NULL) {
        // 调用注册的播放数据回调函数（SkOpusDecFeedPlayAudio）
        rcv_bytes = ctrl->funcGetPlayData(buff, len, &timeRecord);
        ctrl->playDataCnt++;
    }
    
    // 如果暂停或没有回调函数，填充静音
    if ((ctrl->pauseFlag == 1) || (ctrl->funcGetPlayData == NULL)) {
        memset(buff, 0, len);
        rcv_bytes = len;
    }
    
    return rcv_bytes;
}
```

### **阶段6: 硬件音频输出**

#### **6.1 板级音频播放接口**
```c
// main/board/sk_board_aec.c:114
sk_err_t SkBspPlayAudio(const int16_t* data, size_t length, uint32_t msToWait) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevWriteNoncodec(data, length, msToWait);  // 直接I2S输出
#else
    return AudioDevWrite(data, length);                    // ES8311 DAC输出
#endif
}
```

#### **6.2 ES8311 DAC输出**
```c
// main/board/audio_8311_7210.c:240
int AudioDevWrite(const int16_t* data, int dataLen) {
#if CONFIG_SPK_ENABLE
    SkAudioDev *audioDev = &g_audioDev;
    
    if (audioDev->spkEnabled) {
        // 通过ESP编解码器框架写入ES8311 DAC
        ESP_ERROR_CHECK_WITHOUT_ABORT(esp_codec_dev_write(audioDev->spkDev, (void*)data, dataLen));
    }
    return ESP_OK;
#endif
}
```

## 🔗 **关键回调链路配置**

### **初始化时的回调设置**
```c
// main/app/main.c:147,152
SkWsRegOnBinDataCallback(MyAppOnWsEvent, NULL);     // WebSocket → MyApp
SkPlayerSetCallback(SkOpusDecFeedPlayAudio);        // Player → OpusDec
```

### **完整回调链**
```
WebSocket接收 → SkWsProcFrame()
    ↓
二进制帧处理 → MyAppOnWsEvent()
    ↓
音频数据解析 → MyAppOnWsAudioData()
    ↓
Opus解码 → SkOpusDecPlayRemote()
    ↓
播放回调 → SkOpusDecFeedPlayAudio()  [回调1]
    ↓
播放任务 → PlayerGetData()
    ↓
硬件输出 → SkBspPlayAudio()
```

## 📈 **性能特征**

### **数据包格式**
```c
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01 - 协议版本
    uint8_t type;           // 0x01 - 音频类型
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} WsAudioPacket;
```

### **音频参数**
- **采样率**: 16kHz
- **位深**: 16bit
- **声道**: 单声道
- **帧长**: 20ms (320 samples)
- **Opus数据**: ~20-60 bytes/frame
- **PCM数据**: 640 bytes/frame

### **延迟分析**
- **网络延迟**: 取决于网络条件
- **WebSocket解析**: ~1ms
- **Opus解码**: ~5ms
- **播放缓冲**: ~20ms (一帧时间)
- **总延迟**: 网络延迟 + ~26ms

### **缓冲区管理**
- **WebSocket接收缓冲**: 4KB
- **解码输入队列**: 4个缓冲区
- **解码输出队列**: 4个缓冲区
- **播放缓冲区**: 640 bytes
- **总内存占用**: ~25KB

## 🎯 **总结**

sk-terminal的WebSocket音频接收系统实现了一个高效的音频播放管道：

1. **网络层**: WebSocket协议接收 → 帧解析
2. **应用层**: 音频包解析 → 数据验证
3. **编解码层**: Opus解码 → PCM转换
4. **播放层**: 音频缓冲 → 播放调度
5. **硬件层**: ES8311 DAC → 扬声器输出

整个系统通过回调机制和消息队列实现了高效的数据流转，同时保持了低延迟和高音质的播放效果。
